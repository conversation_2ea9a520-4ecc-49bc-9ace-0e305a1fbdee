#!/usr/bin/env python3
"""
Script to download and install LLVM for Web Stable Diffusion build
"""

import os
import sys
import urllib.request
import zipfile
import tarfile
import shutil
from pathlib import Path

def download_file(url, filename):
    """Download a file with progress indication"""
    print(f"Downloading {filename}...")
    try:
        urllib.request.urlretrieve(url, filename)
        print(f"✅ Downloaded {filename}")
        return True
    except Exception as e:
        print(f"❌ Failed to download {filename}: {e}")
        return False

def extract_archive(archive_path, extract_to):
    """Extract archive file"""
    print(f"Extracting {archive_path}...")
    try:
        if archive_path.endswith('.tar.xz'):
            with tarfile.open(archive_path, 'r:xz') as tar:
                tar.extractall(extract_to)
        elif archive_path.endswith('.zip'):
            with zipfile.ZipFile(archive_path, 'r') as zip_ref:
                zip_ref.extractall(extract_to)
        print(f"✅ Extracted to {extract_to}")
        return True
    except Exception as e:
        print(f"❌ Failed to extract {archive_path}: {e}")
        return False

def main():
    print("🔧 LLVM Installation Script for Web Stable Diffusion")
    print("=" * 50)
    
    # LLVM download URLs (you may need to update these to the latest version)
    llvm_version = "20.1.7"
    llvm_url = f"https://github.com/llvm/llvm-project/releases/download/llvmorg-{llvm_version}/clang+llvm-{llvm_version}-x86_64-pc-windows-msvc.tar.xz"
    
    # Create downloads directory
    downloads_dir = Path("downloads")
    downloads_dir.mkdir(exist_ok=True)
    
    # Create LLVM installation directory
    llvm_dir = Path("llvm")
    if llvm_dir.exists():
        print(f"⚠️  LLVM directory already exists: {llvm_dir}")
        response = input("Do you want to remove it and reinstall? (y/N): ")
        if response.lower() == 'y':
            shutil.rmtree(llvm_dir)
        else:
            print("Installation cancelled.")
            return
    
    llvm_dir.mkdir(exist_ok=True)
    
    # Download LLVM
    archive_name = f"clang+llvm-{llvm_version}-x86_64-pc-windows-msvc.tar.xz"
    archive_path = downloads_dir / archive_name
    
    if not archive_path.exists():
        if not download_file(llvm_url, str(archive_path)):
            print("❌ Failed to download LLVM. Please download manually from:")
            print(f"   {llvm_url}")
            print("   and place it in the downloads/ directory")
            return
    else:
        print(f"✅ Using existing download: {archive_path}")
    
    # Extract LLVM
    if not extract_archive(str(archive_path), str(llvm_dir)):
        return
    
    # Find the extracted directory (it usually has a version-specific name)
    extracted_dirs = [d for d in llvm_dir.iterdir() if d.is_dir()]
    if not extracted_dirs:
        print("❌ No directories found after extraction")
        return
    
    llvm_bin_dir = extracted_dirs[0] / "bin"
    if not llvm_bin_dir.exists():
        print(f"❌ bin directory not found in {extracted_dirs[0]}")
        return
    
    print(f"✅ LLVM installed to: {llvm_bin_dir}")
    
    # Check if clang.exe exists
    clang_exe = llvm_bin_dir / "clang.exe"
    if clang_exe.exists():
        print(f"✅ clang.exe found: {clang_exe}")
    else:
        print(f"❌ clang.exe not found in {llvm_bin_dir}")
        return
    
    # Add to PATH for current session
    current_path = os.environ.get('PATH', '')
    if str(llvm_bin_dir) not in current_path:
        os.environ['PATH'] = str(llvm_bin_dir) + os.pathsep + current_path
        print(f"✅ Added {llvm_bin_dir} to PATH for current session")
    
    print("\n🎉 LLVM installation completed!")
    print("\n📋 Next steps:")
    print("1. Add the following directory to your system PATH permanently:")
    print(f"   {llvm_bin_dir.absolute()}")
    print("2. Or run the build command in this same terminal session")
    print("3. Run: python build.py --target llvm --use-cache=1")
    
    # Test clang
    try:
        import subprocess
        result = subprocess.run([str(clang_exe), '--version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"\n✅ clang test successful:")
            print(result.stdout.split('\n')[0])
        else:
            print(f"\n⚠️  clang test failed with return code {result.returncode}")
    except Exception as e:
        print(f"\n⚠️  Could not test clang: {e}")

if __name__ == "__main__":
    main()
