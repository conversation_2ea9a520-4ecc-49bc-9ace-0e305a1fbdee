#!/usr/bin/env python3
"""
Simple HTTP server for Web Stable Diffusion demo
"""
import http.server
import socketserver
import os
import json
from urllib.parse import urlparse, parse_qs

class WebSDHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=".", **kwargs)
    
    def do_GET(self):
        # Parse the URL
        parsed_path = urlparse(self.path)
        
        # Serve the main page
        if parsed_path.path == "/" or parsed_path.path == "/index.html":
            self.serve_index()
        elif parsed_path.path == "/status":
            self.serve_status()
        else:
            # Try to serve static files
            super().do_GET()
    
    def do_POST(self):
        # Handle API requests
        parsed_path = urlparse(self.path)
        
        if parsed_path.path == "/api/generate":
            self.handle_generate()
        else:
            self.send_error(404, "Not Found")
    
    def serve_index(self):
        """Serve the main HTML page"""
        try:
            print(f"DEBUG: Attempting to read web/stable_diffusion.html from {os.getcwd()}")
            print(f"DEBUG: File exists: {os.path.exists('web/stable_diffusion.html')}")

            with open("web/stable_diffusion.html", "r", encoding="utf-8") as f:
                content = f.read()

            print(f"DEBUG: Successfully read file, content length: {len(content)}")

            # Wrap in basic HTML structure
            html = f"""<!DOCTYPE html>
<html>
<head>
    <title>Web Stable Diffusion</title>
    <meta charset="utf-8">
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .container {{ max-width: 800px; margin: 0 auto; }}
        .status {{ background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 5px; }}
        .error {{ background: #ffe6e6; color: #cc0000; }}
        .warning {{ background: #fff3cd; color: #856404; }}
        input, select, button {{ margin: 5px; padding: 5px; }}
        canvas {{ border: 1px solid #ccc; }}
    </style>
</head>
<body>
    <div class="container">
        <h1>Web Stable Diffusion Demo</h1>
        <div class="status warning">
            <strong>Note:</strong> This is a demo server. The actual model files need to be built first using the build.py script.
        </div>
        {content}
        <div class="status">
            <h3>Server Status</h3>
            <p>Server is running on port 8887</p>
            <p>You can test with: <code>curl http://localhost:8887/status</code></p>
        </div>
    </div>
</body>
</html>"""

            print(f"DEBUG: Sending response, HTML length: {len(html)}")

            self.send_response(200)
            self.send_header("Content-type", "text/html")
            self.send_header("Content-Length", str(len(html.encode())))
            self.end_headers()
            self.wfile.write(html.encode())

        except FileNotFoundError as e:
            print(f"DEBUG: FileNotFoundError: {e}")
            self.send_error(404, "HTML file not found")
        except Exception as e:
            print(f"DEBUG: Unexpected error: {e}")
            self.send_error(500, f"Server error: {str(e)}")
    
    def serve_status(self):
        """Serve status information"""
        status = {
            "status": "running",
            "message": "Web Stable Diffusion server is running",
            "models_available": False,
            "note": "Model files need to be built using build.py script"
        }
        
        # Check if model files exist
        if os.path.exists("dist"):
            status["dist_folder"] = True
            if os.path.exists("dist/stable_diffusion_webgpu.wasm"):
                status["webgpu_model"] = True
            if os.path.exists("dist/params"):
                status["params"] = True
        
        response = json.dumps(status, indent=2)
        
        self.send_response(200)
        self.send_header("Content-type", "application/json")
        self.send_header("Content-Length", str(len(response.encode())))
        self.end_headers()
        self.wfile.write(response.encode())
    
    def handle_generate(self):
        """Handle image generation requests"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode())
            
            # Mock response since we don't have the actual model
            response = {
                "status": "error",
                "message": "Model not available. Please build the model first using build.py",
                "prompt": data.get("prompt", ""),
                "steps": data.get("steps", 20)
            }
            
            response_json = json.dumps(response)
            
            self.send_response(503)  # Service Unavailable
            self.send_header("Content-type", "application/json")
            self.send_header("Content-Length", str(len(response_json.encode())))
            self.end_headers()
            self.wfile.write(response_json.encode())
            
        except Exception as e:
            error_response = {"status": "error", "message": str(e)}
            response_json = json.dumps(error_response)
            
            self.send_response(500)
            self.send_header("Content-type", "application/json")
            self.send_header("Content-Length", str(len(response_json.encode())))
            self.end_headers()
            self.wfile.write(response_json.encode())

def main():
    PORT = 8887

    print(f"Starting Web Stable Diffusion server on port {PORT}")
    print(f"Open http://localhost:{PORT} in your browser")
    print(f"Test with: curl http://localhost:{PORT}/status")
    
    with socketserver.TCPServer(("", PORT), WebSDHandler) as httpd:
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\nServer stopped.")

if __name__ == "__main__":
    main()
