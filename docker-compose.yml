version: '3.8'

services:
  web-stable-diffusion:
    build:
      context: .
      dockerfile: Dockerfile
    image: web-stable-diffusion:perfect
    container_name: web-stable-diffusion-perfect
    volumes:
      # Model cache directories to avoid re-downloading
      - ./cache/huggingface:/root/.cache/huggingface
      - ./cache/torch:/root/.cache/torch
      - ./cache/pip:/root/.cache/pip
      - ./cache/apt:/var/cache/apt
      - ./cache/conda:/opt/conda/pkgs
      # Persistent storage for compiled models
      - ./artifacts:/workspace/artifacts
      - ./dist:/workspace/dist
    working_dir: /workspace
    environment:
      - PYTHONUNBUFFERED=1
      - TVM_HOME=/opt/tvm
      - PYTHONPATH=/workspace:/opt/tvm/python
      - HF_HOME=/root/.cache/huggingface
      - TORCH_HOME=/root/.cache/torch
      - PIP_CACHE_DIR=/root/.cache/pip
      # Disable CUDA for CPU-only mode
      - CUDA_VISIBLE_DEVICES=""
    ports:
      - "8887:8887"
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost:8887/health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    stdin_open: true
    tty: true
    networks:
      - web-stable-diffusion-net

  web-stable-diffusion-dev:
    build:
      context: .
      dockerfile: Dockerfile
    image: web-stable-diffusion:perfect
    container_name: web-stable-diffusion-dev
    volumes:
      # Development mode - mount source code
      - .:/workspace
      # Shared cache directories
      - ./cache/huggingface:/root/.cache/huggingface
      - ./cache/torch:/root/.cache/torch
      - ./cache/pip:/root/.cache/pip
      - ./cache/apt:/var/cache/apt
      - ./cache/conda:/opt/conda/pkgs
    working_dir: /workspace
    environment:
      - PYTHONUNBUFFERED=1
      - TVM_HOME=/opt/tvm
      - PYTHONPATH=/workspace:/opt/tvm/python
      - HF_HOME=/root/.cache/huggingface
      - TORCH_HOME=/root/.cache/torch
      - PIP_CACHE_DIR=/root/.cache/pip
      - CUDA_VISIBLE_DEVICES=""
    ports:
      - "8888:8887"  # Different port for dev
    stdin_open: true
    tty: true
    command: bash
    networks:
      - web-stable-diffusion-net

networks:
  web-stable-diffusion-net:
    driver: bridge
