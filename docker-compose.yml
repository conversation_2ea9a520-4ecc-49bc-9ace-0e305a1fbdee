version: '3.8'

services:
  web-stable-diffusion:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: web-sd-build
    volumes:
      - .:/workspace
      - ./dist:/workspace/dist
    working_dir: /workspace
    environment:
      - PYTHONUNBUFFERED=1
      - TVM_HOME=/opt/tvm
      - PYTHONPATH=/opt/tvm/python
    ports:
      - "8887:8887"
    stdin_open: true
    tty: true
    command: bash
