version: '3.8'

services:
  web-stable-diffusion:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: web-stable-diffusion
    volumes:
      - ./dist:/workspace/dist
    working_dir: /workspace
    environment:
      - PYTHONUNBUFFERED=1
      - TVM_HOME=/opt/tvm
      - PYTHONPATH=/opt/tvm/python
    ports:
      - "8887:8887"
    stdin_open: true
    tty: true

  web-stable-diffusion-dev:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: web-stable-diffusion-dev
    volumes:
      - .:/workspace
    working_dir: /workspace
    environment:
      - PYTHONUNBUFFERED=1
      - TVM_HOME=/opt/tvm
      - PYTHONPATH=/opt/tvm/python
    ports:
      - "8887:8887"
    stdin_open: true
    tty: true
    command: bash
