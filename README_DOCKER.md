# 🐳 Web Stable Diffusion Docker Solution

这是一个完整的Docker化Web Stable Diffusion解决方案，包含了完整的模型编译、构建和Web服务器。

## 🚀 快速开始

### 方法1: 使用构建脚本（推荐）

```bash
# 构建并运行完整解决方案
./docker_build.sh run

# 或者分步执行：
./docker_build.sh build  # 构建Docker镜像
./docker_build.sh run    # 运行容器
```

### 方法2: 使用Docker Compose

```bash
# 构建并运行
docker-compose up web-stable-diffusion

# 开发模式
docker-compose up web-stable-diffusion-dev
```

### 方法3: 直接使用Docker

```bash
# 构建镜像
docker build -t web-stable-diffusion:latest .

# 运行容器
docker run -it --rm -p 8887:8887 web-stable-diffusion:latest
```

## 📋 容器功能

当容器启动时，它会自动：

1. **🏗️ 编译模型**: 完整编译Web Stable Diffusion模型
2. **🧪 运行测试**: 验证构建结果
3. **🌐 启动Web服务器**: 在端口8887上启动Web界面

## 🌐 访问Web界面

容器启动后，在浏览器中访问：
```
http://localhost:8887
```

## 📁 构建产物

编译完成后，以下文件将在`dist/`目录中：

```
dist/
├── mod_cache_before_build.pkl          # 编译的TVM模块
├── stable_diffusion_module.tar         # TVM模块存档
├── params/                             # 模型参数
│   ├── ndarray-cache-b16.json          # 参数元数据
│   └── params_shard_*.bin              # 参数数据分片
├── scheduler_dpm_solver_multistep_consts.json
└── scheduler_pndm_consts.json
```

## 🛠️ 开发模式

如果您想要进入容器进行开发：

```bash
./docker_build.sh dev
```

这将启动一个带有bash shell的容器，您可以：
- 修改代码
- 重新构建模型
- 调试问题

## 📊 系统要求

- **Docker**: 20.10+
- **内存**: 至少8GB RAM
- **存储**: 至少10GB可用空间
- **网络**: 首次构建需要下载依赖

## ⏱️ 构建时间

- **首次构建**: 20-30分钟（包括下载和编译TVM）
- **后续构建**: 5-10分钟（使用Docker缓存）

## 🔧 故障排除

### 构建失败
```bash
# 清理并重新构建
docker system prune -f
./docker_build.sh build
```

### 端口冲突
```bash
# 使用不同端口
docker run -it --rm -p 8888:8887 web-stable-diffusion:latest
```

### 内存不足
```bash
# 增加Docker内存限制（Docker Desktop设置）
# 或者使用更小的模型配置
```

## 📝 技术细节

### 包含的组件
- **Ubuntu 22.04**: 基础系统
- **Python 3.10**: 运行环境
- **TVM Unity**: 深度学习编译器
- **LLVM 14**: 编译工具链
- **PyTorch**: 深度学习框架
- **Transformers**: Hugging Face模型库

### 编译目标
- **Target**: LLVM (CPU)
- **精度**: bfloat16
- **优化**: 启用所有TVM优化

### 模型组件
- **Text Encoder**: CLIP模型
- **Diffusion Model**: UNet
- **Image Decoder**: VAE
- **Schedulers**: DPM和PNDM

## 🎯 使用场景

1. **开发测试**: 在隔离环境中测试Web Stable Diffusion
2. **部署**: 作为生产环境的基础镜像
3. **研究**: 修改和实验不同的模型配置
4. **教学**: 学习深度学习模型编译和部署

## 🤝 贡献

如果您遇到问题或有改进建议：
1. 检查现有的issues
2. 创建新的issue描述问题
3. 提交pull request

## 📄 许可证

本项目遵循原始Web Stable Diffusion项目的许可证。

---

**🎉 享受您的Web Stable Diffusion Docker体验！**
