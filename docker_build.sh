#!/bin/bash
set -e

echo "🐳 Starting Web Stable Diffusion Docker Build"
echo "=============================================="

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

# Check if Docker Compose is available
if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
    echo "❌ Docker Compose is not available"
    exit 1
fi

# Build the Docker image
echo "🔨 Building Docker image..."
if command -v docker-compose &> /dev/null; then
    docker-compose build
else
    docker compose build
fi

# Start the container and run the build
echo "🚀 Starting container and building model..."
if command -v docker-compose &> /dev/null; then
    docker-compose run --rm web-stable-diffusion bash -c "
        echo '📦 Installing additional Python dependencies...'
        pip3 install numpy scipy pillow requests tqdm
        
        echo '🔍 Checking TVM installation...'
        python3 -c 'import tvm; print(f\"TVM version: {tvm.__version__}\"); import tvm.relax; print(\"Relax module available\")'
        
        echo '🏗️ Building Web Stable Diffusion model...'
        python3 build.py --target llvm --use-cache=0
        
        echo '✅ Build completed! Files are in ./dist/'
        ls -la dist/
    "
else
    docker compose run --rm web-stable-diffusion bash -c "
        echo '📦 Installing additional Python dependencies...'
        pip3 install numpy scipy pillow requests tqdm
        
        echo '🔍 Checking TVM installation...'
        python3 -c 'import tvm; print(f\"TVM version: {tvm.__version__}\"); import tvm.relax; print(\"Relax module available\")'
        
        echo '🏗️ Building Web Stable Diffusion model...'
        python3 build.py --target llvm --use-cache=0
        
        echo '✅ Build completed! Files are in ./dist/'
        ls -la dist/
    "
fi

echo "🎉 Docker build process completed!"
echo "📁 Check the ./dist/ directory for generated files"
