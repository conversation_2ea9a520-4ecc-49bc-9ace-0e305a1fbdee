#!/bin/bash
set -e

echo "🐳 Web Stable Diffusion Complete Docker Solution"
echo "================================================"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

# Function to build the Docker image
build_image() {
    echo "🔨 Building Docker image (this may take 20-30 minutes)..."
    docker build -t web-stable-diffusion:latest . || {
        echo "❌ Docker build failed"
        exit 1
    }
    echo "✅ Docker image built successfully!"
}

# Function to run the container
run_container() {
    echo "🚀 Starting Web Stable Diffusion container..."
    echo "📝 The container will:"
    echo "   1. Build the complete model"
    echo "   2. Run tests to verify the build"
    echo "   3. Start the web server on port 8887"
    echo ""
    echo "🌐 Once started, you can access the web interface at:"
    echo "   http://localhost:8887"
    echo ""

    docker run -it --rm \
        -p 8887:8887 \
        -v "$(pwd)/dist:/workspace/dist" \
        --name web-stable-diffusion \
        web-stable-diffusion:latest
}

# Function to run in development mode
run_dev() {
    echo "🛠️ Starting development container..."
    docker run -it --rm \
        -p 8887:8887 \
        -v "$(pwd):/workspace" \
        --name web-stable-diffusion-dev \
        web-stable-diffusion:latest bash
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [build|run|dev|help]"
    echo ""
    echo "Commands:"
    echo "  build  - Build the Docker image"
    echo "  run    - Run the complete Web Stable Diffusion container"
    echo "  dev    - Start a development container with bash shell"
    echo "  help   - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 build    # Build the Docker image"
    echo "  $0 run      # Run the complete solution"
    echo "  $0 dev      # Start development environment"
}

# Main script logic
case "${1:-run}" in
    "build")
        build_image
        ;;
    "run")
        # Check if image exists, build if not
        if ! docker image inspect web-stable-diffusion:latest >/dev/null 2>&1; then
            echo "🔍 Docker image not found, building first..."
            build_image
        fi
        run_container
        ;;
    "dev")
        # Check if image exists, build if not
        if ! docker image inspect web-stable-diffusion:latest >/dev/null 2>&1; then
            echo "🔍 Docker image not found, building first..."
            build_image
        fi
        run_dev
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        echo "❌ Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
