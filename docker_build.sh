#!/bin/bash
set -e

echo "🐳 Web Stable Diffusion Complete Docker Solution"
echo "================================================"

# Check if Docker is available
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed or not in PATH"
    exit 1
fi

# Function to build the Docker image
build_image() {
    echo "🔨 Building Docker image (this may take 20-30 minutes)..."
    docker build -t web-stable-diffusion:latest . || {
        echo "❌ Docker build failed"
        exit 1
    }
    echo "✅ Docker image built successfully!"
}

# Function to build simple Docker image
build_simple() {
    echo "🔨 Building Simple Docker image (fast build)..."
    docker build -f Dockerfile.simple -t web-stable-diffusion:simple . || {
        echo "❌ Simple Docker build failed"
        exit 1
    }
    echo "✅ Simple Docker image built successfully!"
}

# Function to run the container
run_container() {
    echo "🚀 Starting Web Stable Diffusion container..."
    echo "📝 The container will:"
    echo "   1. Build the complete model"
    echo "   2. Run tests to verify the build"
    echo "   3. Start the web server on port 8887"
    echo ""
    echo "🌐 Once started, you can access the web interface at:"
    echo "   http://localhost:8887"
    echo ""

    docker run -it --rm \
        -p 8887:8887 \
        -v "$(pwd)/dist:/workspace/dist" \
        --name web-stable-diffusion \
        web-stable-diffusion:latest
}

# Function to run simple container
run_simple() {
    echo "🚀 Starting Simple Web Stable Diffusion container..."
    echo "📝 The container will serve pre-built model files"
    echo ""
    echo "🌐 Once started, you can access the web interface at:"
    echo "   http://localhost:8887"
    echo ""

    docker run -it --rm \
        -p 8887:8887 \
        -v "$(pwd)/dist:/app/dist" \
        --name web-stable-diffusion-simple \
        web-stable-diffusion:simple
}

# Function to run in development mode
run_dev() {
    echo "🛠️ Starting development container..."
    docker run -it --rm \
        -p 8887:8887 \
        -v "$(pwd):/workspace" \
        --name web-stable-diffusion-dev \
        web-stable-diffusion:latest bash
}

# Function to show usage
show_usage() {
    echo "Usage: $0 [build|simple|run|run-simple|dev|help]"
    echo ""
    echo "Commands:"
    echo "  build      - Build the full Docker image (with TVM compilation)"
    echo "  simple     - Build the simple Docker image (fast, serves pre-built files)"
    echo "  run        - Run the complete Web Stable Diffusion container"
    echo "  run-simple - Run the simple container (serves pre-built model)"
    echo "  dev        - Start a development container with bash shell"
    echo "  help       - Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 simple     # Build and run simple version (recommended)"
    echo "  $0 build      # Build the full Docker image"
    echo "  $0 run        # Run the complete solution"
    echo "  $0 run-simple # Run simple version"
    echo "  $0 dev        # Start development environment"
}

# Main script logic
case "${1:-simple}" in
    "build")
        build_image
        ;;
    "simple")
        build_simple
        run_simple
        ;;
    "run")
        # Check if image exists, build if not
        if ! docker image inspect web-stable-diffusion:latest >/dev/null 2>&1; then
            echo "🔍 Docker image not found, building first..."
            build_image
        fi
        run_container
        ;;
    "run-simple")
        # Check if simple image exists, build if not
        if ! docker image inspect web-stable-diffusion:simple >/dev/null 2>&1; then
            echo "🔍 Simple Docker image not found, building first..."
            build_simple
        fi
        run_simple
        ;;
    "dev")
        # Check if image exists, build if not
        if ! docker image inspect web-stable-diffusion:latest >/dev/null 2>&1; then
            echo "🔍 Docker image not found, building first..."
            build_image
        fi
        run_dev
        ;;
    "help"|"-h"|"--help")
        show_usage
        ;;
    *)
        echo "❌ Unknown command: $1"
        show_usage
        exit 1
        ;;
esac
