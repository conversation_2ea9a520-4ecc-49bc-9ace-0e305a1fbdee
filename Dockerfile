# Web Stable Diffusion Docker Build Environment
FROM python:3.11-slim

# Set environment variables
ENV DEBIAN_FRONTEND=noninteractive
ENV TZ=UTC
ENV PYTHONUNBUFFERED=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    git \
    wget \
    curl \
    llvm-14 \
    llvm-14-dev \
    clang-14 \
    libedit-dev \
    libxml2-dev \
    ninja-build \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Create symlinks for LLVM tools
RUN ln -s /usr/bin/llvm-config-14 /usr/bin/llvm-config && \
    ln -s /usr/bin/clang-14 /usr/bin/clang && \
    ln -s /usr/bin/clang++-14 /usr/bin/clang++

# Set working directory
WORKDIR /workspace

# Install Python dependencies
RUN pip install --no-cache-dir torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cpu
RUN pip install --no-cache-dir transformers diffusers accelerate numpy scipy pillow requests tqdm

# Install TVM from source (Unity branch for Relax support)
RUN git clone --recursive --branch unity https://github.com/apache/tvm.git /opt/tvm
WORKDIR /opt/tvm
RUN mkdir build && cd build && \
    cmake -G Ninja \
    -DCMAKE_BUILD_TYPE=Release \
    -DUSE_LLVM=ON \
    -DUSE_CUDA=OFF \
    -DUSE_OPENCL=OFF \
    -DUSE_VULKAN=OFF \
    -DUSE_METAL=OFF \
    -DUSE_ROCM=OFF \
    -DUSE_HEXAGON=OFF \
    -DUSE_TENSORRT=OFF \
    -DUSE_MIOPEN=OFF \
    -DUSE_CUDNN=OFF \
    -DUSE_CUBLAS=OFF \
    -DUSE_THRUST=OFF \
    -DUSE_SORT=ON \
    -DUSE_NNPACK=OFF \
    -DUSE_LIBTORCH=OFF \
    -DUSE_MSVC_MT=OFF \
    -DUSE_MICRO=OFF \
    -DUSE_INSTALL_DEV=ON \
    -DHIDE_PRIVATE_SYMBOLS=ON \
    -DUSE_PROFILER=ON \
    .. && \
    ninja && \
    ninja install

# Set TVM environment variables
ENV TVM_HOME=/opt/tvm
ENV PYTHONPATH=$TVM_HOME/python:$PYTHONPATH
ENV PATH=$TVM_HOME/build:$PATH

# Install TVM Python package
RUN cd /opt/tvm/python && pip install -e .

# Go back to workspace
WORKDIR /workspace

# Copy the project files
COPY . .

# Create dist directory
RUN mkdir -p dist

# Set the default command
CMD ["bash"]
